// RTL Support for Broker Title - تخطيط منسق شبه الإنجليزي
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .broker-pages {
    .card {
      .card-body {
        .row.align-items-center.mb-3 {
          // إبقاء التخطيط الأفقي زي الإنجليزي
          flex-direction: row !important;
          align-items: center !important;

          // الصورة في الشمال
          .col-12.col-sm-auto {
            text-align: center !important;
            margin-bottom: 1rem !important;

            .symbol.symbol-65px {
              width: 80px !important;
              height: 80px !important;

              img {
                width: 80px !important;
                height: 80px !important;
                object-fit: cover !important;
                border-radius: 50% !important; // دائرية
                border: 2px solid #e9ecef !important;
              }
            }
          }

          // المحتوى في اليمين
          .col {
            .row.justify-content-between {
              .col-12.col-lg-9 {
                text-align: right !important;

                // الاسم
                .mb-2 {
                  h2 {
                    font-family: var(--arabic-font-primary) !important;
                    font-size: 1.75rem !important;
                    text-align: right !important;
                    margin-bottom: 0.75rem !important;

                    a {
                      text-decoration: none !important;

                      &:hover {
                        color: var(--bs-primary) !important;
                      }
                    }
                  }
                }

                // الـ badges
                .row {
                  .col-12.d-flex.flex-wrap {
                    justify-content: flex-start !important;
                    direction: rtl !important;

                    .badge {
                      margin-left: 0 !important;
                      margin-right: 0.5rem !important;
                      margin-bottom: 0.5rem !important;
                      font-family: var(--arabic-font-secondary) !important;
                      font-size: 0.8rem !important;
                      padding: 0.4rem 0.8rem !important;
                    }
                  }
                }
              }

              // الزر في اليسار
              .col-12.col-lg-auto {
                text-align: left !important;

                .btn {
                  direction: ltr !important;
                  padding: 0.5rem 1rem !important;
                  font-size: 0.9rem !important;
                  font-weight: 600 !important;

                  .fa-solid {
                    margin-left: 0.5rem !important;
                    margin-right: 0 !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

}

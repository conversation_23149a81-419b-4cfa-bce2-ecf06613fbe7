// RTL Support for Broker Title
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .broker-pages {
    .card {
      .card-body {
        text-align: right;

        .row.align-items-center {
          .col {
            .row.justify-content-between {
              .col-12.col-lg-9 {
                text-align: right;

                .d-flex.align-items-center.flex-wrap {
                  flex-direction: row-reverse !important;
                  justify-content: flex-end !important;

                  span {
                    order: 2;
                    margin-left: 0.5rem !important;
                    margin-right: 0 !important;
                  }

                  a {
                    order: 1;
                    margin-left: 0 !important;
                    margin-right: 0.5rem !important;
                  }
                }

                .row {
                  .col-12.d-flex.flex-wrap {
                    justify-content: flex-end !important;

                    .badge {
                      margin-left: 0.5rem !important;
                      margin-right: 0 !important;
                    }
                  }
                }
              }

              .col-12.col-lg-auto {
                text-align: right !important;

                .btn {
                  .fa-solid {
                    margin-left: 0.5rem !important;
                    margin-right: 0 !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // إصلاح خاص للنص والاسم
  .text-gray-800.fs-2.fw-bolder,
  .text-gray-800.text-hover-dark-blue.fs-2.fw-semibold {
    font-family: var(--arabic-font-primary) !important;
    text-align: right !important;
  }

  // إصلاح الـ badges
  .badge {
    font-family: var(--arabic-font-secondary) !important;
    text-align: center !important;
  }

  // إصلاح عام للترتيب
  .d-flex.align-items-center.flex-wrap.mb-2 {
    direction: rtl !important;
    text-align: right !important;
    justify-content: flex-start !important;

    span.text-gray-800.fs-2.fw-bolder {
      order: 1 !important;
      margin-left: 0 !important;
      margin-right: 0.5rem !important;
    }

    a.text-gray-800.text-hover-dark-blue.fs-2.fw-semibold {
      order: 2 !important;
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }
  }

  // إصلاح خاص للـ badges layout
  .col-12.d-flex.flex-wrap {
    direction: rtl !important;
    justify-content: flex-start !important;

    .badge {
      margin-left: 0 !important;
      margin-right: 0.5rem !important;
      margin-bottom: 0.5rem !important;
    }
  }

  // إصلاح زر إنشاء الطلب
  .col-12.col-lg-auto {
    text-align: left !important;

    .btn {
      direction: ltr !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
    }
  }

  // إصلاح عام للكارد
  .card .card-body {
    .row.align-items-center.mb-3 {
      .col-12.col-sm-auto {
        text-align: center !important;
      }

      .col {
        .row.justify-content-between {
          justify-content: space-between !important;
          align-items: flex-start !important;
        }
      }
    }
  }

  // إصلاح التصميم الجديد - صورة أكبر وزر تحتها
  .card .card-body {
    .row.align-items-center {
      .col-12.col-md-3 {
        text-align: center !important;

        .symbol.symbol-100px {
          margin-bottom: 1rem !important;

          img {
            width: 100px !important;
            height: 100px !important;
            object-fit: cover !important;
            border: 3px solid #f8f9fa !important;
          }
        }

        .btn {
          font-size: 0.875rem !important;
          padding: 0.5rem 1rem !important;
          min-width: 150px !important;

          .fa-solid {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }
        }
      }

      .col-12.col-md-9 {
        text-align: right !important;

        h2 {
          font-family: var(--arabic-font-primary) !important;
          font-size: 2rem !important;
          margin-bottom: 1rem !important;
          line-height: 1.2 !important;

          a {
            text-decoration: none !important;

            &:hover {
              color: var(--bs-primary) !important;
            }
          }
        }

        .d-flex.flex-wrap {
          justify-content: flex-start !important;
          gap: 0.5rem !important;

          .badge {
            margin-left: 0 !important;
            margin-right: 0 !important;
            font-family: var(--arabic-font-secondary) !important;
            font-size: 0.75rem !important;
            padding: 0.5rem 0.75rem !important;
          }
        }
      }
    }
  }
}

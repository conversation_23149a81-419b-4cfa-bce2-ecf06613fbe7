// RTL Support for Broker Title
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .broker-pages {
    .card {
      .card-body {
        text-align: right;

        .row.align-items-center {
          .col {
            .row.justify-content-between {
              .col-12.col-lg-9 {
                text-align: right;

                .d-flex.align-items-center.flex-wrap {
                  flex-direction: row-reverse !important;
                  justify-content: flex-end !important;

                  span {
                    order: 2;
                    margin-left: 0.5rem !important;
                    margin-right: 0 !important;
                  }

                  a {
                    order: 1;
                    margin-left: 0 !important;
                    margin-right: 0.5rem !important;
                  }
                }

                .row {
                  .col-12.d-flex.flex-wrap {
                    justify-content: flex-end !important;

                    .badge {
                      margin-left: 0.5rem !important;
                      margin-right: 0 !important;
                    }
                  }
                }
              }

              .col-12.col-lg-auto {
                text-align: right !important;

                .btn {
                  .fa-solid {
                    margin-left: 0.5rem !important;
                    margin-right: 0 !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // إصلاح خاص للنص والاسم
  .text-gray-800.fs-2.fw-bolder,
  .text-gray-800.text-hover-dark-blue.fs-2.fw-semibold {
    font-family: var(--arabic-font-primary) !important;
    text-align: right !important;
  }

  // إصلاح الـ badges
  .badge {
    font-family: var(--arabic-font-secondary) !important;
    text-align: center !important;
  }

  // إصلاح خاص للغة العربية فقط
  .broker-pages {
    .card .card-body {
      .row.align-items-center.mb-3 {
        // تحسين الصورة - مربعة وأكبر
        .col-12.col-sm-auto {
          text-align: center !important;

          .symbol.symbol-65px {
            width: 120px !important;
            height: 120px !important;

            img {
              width: 120px !important;
              height: 120px !important;
              object-fit: cover !important;
              border-radius: 8px !important; // مربعة مع زوايا مدورة قليلاً
              border: 3px solid #f8f9fa !important;
            }
          }
        }

        .col {
          .row.justify-content-between {
            .col-12.col-lg-9 {
              text-align: right !important;

              // تحسين عرض الاسم
              .mb-2 {
                h2 {
                  font-family: var(--arabic-font-primary) !important;
                  font-size: 2rem !important;
                  line-height: 1.2 !important;
                  margin-bottom: 1rem !important;

                  a {
                    text-decoration: none !important;

                    &:hover {
                      color: var(--bs-primary) !important;
                    }
                  }
                }
              }

              // تحسين الـ badges
              .row {
                .col-12.d-flex.flex-wrap {
                  justify-content: flex-start !important;
                  direction: rtl !important;

                  .badge {
                    margin-left: 0 !important;
                    margin-right: 0.5rem !important;
                    margin-bottom: 0.5rem !important;
                    font-family: var(--arabic-font-secondary) !important;
                  }
                }
              }
            }

            // تحسين الزر - أصغر مع فونت أكبر
            .col-12.col-lg-auto {
              text-align: left !important;

              .btn {
                direction: ltr !important;
                padding: 0.4rem 0.8rem !important; // أصغر
                font-size: 1rem !important; // فونت أكبر
                font-weight: 600 !important;
                min-width: 120px !important; // عرض أصغر

                .fa-solid {
                  margin-left: 0.5rem !important;
                  margin-right: 0 !important;
                  font-size: 0.9rem !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

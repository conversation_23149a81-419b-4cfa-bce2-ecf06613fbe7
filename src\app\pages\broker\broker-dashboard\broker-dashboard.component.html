<div class="broker-pages">
  <div class="mb-5 mt-0">
    <app-broker-title></app-broker-title>
  </div>

  <div class="row">
    <div class="col-md-6">
      <app-new-request-card [title]="'BROKER.DASHBOARD.CREATE_NEW_REQUEST' | translate"
        [subTitle]="('BROKER.DASHBOARD.OVER' | translate) + ' ' + allRequestsCount + ' ' + (allRequestsCount === 1 ? ('BROKER.DASHBOARD.REQUEST' | translate) : ('BROKER.DASHBOARD.REQUESTS' | translate))"
        [buttonTitle]="'BROKER.DASHBOARD.CREATE' | translate" [buttonIcon]="'plus'"></app-new-request-card>

      <div class="row">
        <div class="col-md-6">
          <app-dashboard-card [title]="'BROKER.DASHBOARD.DEVELOPERS' | translate"
            [subTitle]="('BROKER.DASHBOARD.OVER' | translate) + ' ' + acceptedContractsCount + ' ' + ('BROKER.DASHBOARD.DEVELOPERS' | translate)"
            [icon]="{ type: 'svg', svgContent: developerSvg }" [buttonTitle]="'BROKER.DASHBOARD.VIEW_ALL' | translate"
            [buttonIcon]="'angles-right'" [buttonLink]="'/broker/developers'"></app-dashboard-card>
        </div>
        <div class="col-md-6">
          <app-dashboard-card [title]="'BROKER.DASHBOARD.MY_MAPS' | translate"
            [subTitle]="('BROKER.DASHBOARD.OVER' | translate) + ' ' + maps + ' ' + ('BROKER.DASHBOARD.MAPS' | translate)"
            [icon]="{ type: 'fontawesome', name: 'map' }" [buttonTitle]="'BROKER.DASHBOARD.VIEW_ALL' | translate"
            [buttonIcon]="'angles-right'" [buttonLink]="'/broker/Maps'"></app-dashboard-card>
        </div>
      </div>

      <app-dashboard-card [title]="'BROKER.DASHBOARD.MY_ADVERTISEMENTS' | translate"
        [subTitle]="('BROKER.DASHBOARD.OVER' | translate) + ' ' + advertisements + ' ' + ('BROKER.DASHBOARD.ADS' | translate)"
        [icon]="{ type: 'keenicon', name: 'social-media', iconType: 'outline' }"
        [buttonTitle]="'BROKER.DASHBOARD.VIEW_ALL' | translate" [buttonIcon]="'angles-right'"
        [buttonLink]="'/broker/Adds'"></app-dashboard-card>

      <app-dashboard-card [title]="'BROKER.DASHBOARD.DATA_AND_PROPERTIES' | translate"
        [subTitle]="('BROKER.DASHBOARD.OVER' | translate) + ' ' + dataAndProperties + ' ' + ('BROKER.DASHBOARD.PROPERTIES' | translate)"
        [icon]="{ type: 'svg', svgContent: propertySvg }" [buttonTitle]="'BROKER.DASHBOARD.VIEW_ALL' | translate"
        [buttonIcon]="'angles-right'" [buttonLink]="'/broker/dataandproperties'"></app-dashboard-card>
    </div>

    <div class="col-md-6" *ngIf="allRequestsCount != 0">
      <div class="card mb-4">
        <div class="row g-0">
          <!-- Left Column -->
          <div class="col-md-6">
            <div class="card-header border-0 pb-1">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bolder fs-3 mb-1 text-dark-blue">
                  <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_24_2533)">
                      <path stroke="#0D47A1" stroke-width="1"
                        d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
                      <path stroke="#0D47A1" stroke-width="1"
                        d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
                      <path stroke="#0D47A1" stroke-width="1"
                        d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
                    </g>
                    <defs>
                      <clipPath id="clip0_24_2533">
                        <rect width="19" height="19" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                  {{ 'BROKER.DASHBOARD.REQUESTS_AND_SALES_STATISTICS' | translate }}
                </span>
              </h3>
            </div>
            <div class="card-body pt-0 pb-1">
              <app-analysis-card [backgroundColor]="'success'"
                [title]="'BROKER.DASHBOARD.FINISHED_REQUESTS' | translate" [totalRequests]="allRequestsCount"
                [activeRequests]="finishedRequestsCount"></app-analysis-card>
              <app-analysis-card [backgroundColor]="'mid-blue'"
                [title]="'BROKER.DASHBOARD.IN_PROCESSING_REQUESTS' | translate" [totalRequests]="allRequestsCount"
                [activeRequests]="inProcessingRequestsCount"></app-analysis-card>
            </div>
          </div>

          <!-- Right Column -->
          <div class="col-md-6">
            <div class="card-header border-0 mb-1">
              <h3 class="card-title align-items-start flex-column">
                <span class="card-label fw-bolder fs-3 mb-1 text-dark-blue">{{ 'BROKER.DASHBOARD.FILTER_REQUESTS' |
                  translate }}</span>
                <span class="text-muted fw-bold fs-8">{{ 'BROKER.DASHBOARD.FILTER_BY_SPECIALIZATIONS' | translate
                  }}</span>
              </h3>
              <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-icon btn-light-dark-blue btn-active-dark-blue"
                  (click)="specializationsFilter.toggleDropdown()">
                  <i class="fa-solid fa-filter"></i>
                </button>
                <app-specializations-filter #specializationsFilter (filterChanged)="onFilterChanged($event)"
                  [ngClass]="{'show': specializationsFilter.isDropdownOpen}"></app-specializations-filter>
              </div>
            </div>
            <div class="card-body py-3">
              <div class="row">
                <app-pie-chart *ngIf="allRequestsCount > 0" [newRequestsPercent]="newRequestsPercent"
                  [inProcessingRequestsPercent]="inProcessingRequestsPercent"
                  [finishedRequestsPercent]="finishedRequestsPercent" [chartSize]="150" [chartLine]="30"
                  [chartRotate]="145" cssClass="your-css-class"></app-pie-chart>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-5 mb-5" *ngIf="allRequestsCount != 0">
    <app-new-requests></app-new-requests>
  </div>
</div>

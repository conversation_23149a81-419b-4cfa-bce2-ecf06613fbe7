<!-- <PERSON>er -->
<div class="page-header bg-white rounded-3 shadow-sm mb-6 p-6">
  <div class="d-flex align-items-center justify-content-between">
    <div class="d-flex align-items-center">
      <div class="symbol symbol-50px me-4">
        <div class="symbol-label bg-light-primary">
          <i class="fas fa-bullhorn fs-2 text-primary"></i>
        </div>
      </div>
      <div>
        <h1 class="text-dark fw-bold mb-1">{{ 'BROKER.MY_ADVERTISEMENTS.TITLE' | translate }}</h1>
        <p class="text-muted mb-0 fs-6">
          {{ 'BROKER.MY_ADVERTISEMENTS.SUBTITLE' | translate }}
          <span class="badge badge-light-primary ms-2">
            {{ this.page.totalElements || "0" }} {{ 'BROKER.MY_ADVERTISEMENTS.TOTAL' | translate }}
          </span>
        </p>
      </div>
    </div>

    <div class="d-flex align-items-center gap-3">
      <button class="btn btn-primary btn-sm" (click)="createNewAdvertisement()">
        <i class="fas fa-plus me-2"></i>
        {{ 'BROKER.MY_ADVERTISEMENTS.ADD_NEW' | translate }}
      </button>
    </div>
  </div>
</div>

<div class="tab-pane fade show active" id="kt_tab_pane_1">
  <!-- Empty State - No Advertisements -->
  <div *ngIf="rows.length === 0"
    class="empty-state-container d-flex flex-column align-items-center justify-content-center py-8">
    <div class="empty-state-content text-center">
      <!-- Icon -->
      <div class="empty-state-icon mb-6">
        <div class="symbol symbol-100px mx-auto">
          <div class="symbol-label bg-light-primary">
            <i class="fas fa-bullhorn fs-2x text-primary"></i>
          </div>
        </div>
      </div>

      <!-- Title and Description -->
      <div class="empty-state-text mb-6">
        <h3 class="text-dark fw-bold mb-3">{{ 'BROKER.MY_ADVERTISEMENTS.NO_ADVERTISEMENTS_FOUND' | translate }}</h3>
        <p class="text-muted fs-6 mb-0 mx-auto" style="max-width: 400px;">
          {{ 'BROKER.MY_ADVERTISEMENTS.NO_ADVERTISEMENTS_MESSAGE' | translate }}
        </p>
      </div>

      <!-- Action Button -->
      <div class="empty-state-action">
        <button class="btn btn-primary btn-lg" (click)="createNewAdvertisement()">
          <i class="fas fa-plus me-2"></i>
          {{ 'BROKER.MY_ADVERTISEMENTS.CREATE_NEW_ADVERTISEMENT' | translate }}
        </button>
      </div>

      <!-- Decorative Elements -->
      <div class="empty-state-decoration mt-8">
        <div class="d-flex justify-content-center gap-4">
          <div class="decoration-item">
            <div class="symbol symbol-40px">
              <div class="symbol-label bg-light-success">
                <i class="fas fa-home text-success"></i>
              </div>
            </div>
          </div>
          <div class="decoration-item">
            <div class="symbol symbol-40px">
              <div class="symbol-label bg-light-warning">
                <i class="fas fa-key text-warning"></i>
              </div>
            </div>
          </div>
          <div class="decoration-item">
            <div class="symbol symbol-40px">
              <div class="symbol-label bg-light-info">
                <i class="fas fa-chart-line text-info"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Advertisements Grid -->
  <div *ngIf="rows.length > 0" class="row g-4">
    <!-- Advertisement card for each post -->
    <div class="col-md-3 col-sm-6 col-12" *ngFor="let post of rows; let i = index">
      <div
        class="card h-100 border-0 shadow-sm overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
        (click)="viewPropertyDetails(post)" role="button" tabindex="0">
        <!-- Image slider section -->
        <div class="position-relative">
          <div class="media-container cursor-pointer" (click)="openMediaModal(mediaModal, post, $event)"
            id="mediaContainer_{{ post.id }}">
            <img [src]="getImageFromGallery(post)" class="img-fluid rounded-top"
              style="height: 180px; object-fit: cover; width: 100%; transition: transform 0.3s ease;"
              alt="{{ post.type }} image" />

            <!-- Video play icon overlay -->
            <div *ngIf="isCurrentItemVideo(post)"
              class="video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-30">
              <i class="bi bi-play-circle-fill text-white fs-2"></i>
            </div>

            <!-- Gallery icon overlay -->
            <div *ngIf="!isCurrentItemVideo(post)"
              class="gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-20">
              <i class="bi bi-images text-white fs-3"></i>
            </div>
          </div>

          <!-- Image navigation controls -->
          <div *ngIf="hasMultipleImages(post)"
            class="image-navigation position-absolute top-50 start-0 w-100 d-flex justify-content-between px-2"
            (click)="$event.stopPropagation()">
            <button class="btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue"
              (click)="prevImage(post, $event)" aria-label="Previous image">
              <i class="bi bi-chevron-left fs-6"></i>
            </button>
            <button class="btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue"
              (click)="nextImage(post, $event)" aria-label="Next image">
              <i class="bi bi-chevron-right fs-6"></i>
            </button>
          </div>

          <!-- Image counter -->
          <div *ngIf="hasMultipleImages(post)"
            class="image-counter position-absolute bottom-0 end-0 bg-dark bg-opacity-75 text-white px-2 py-1 rounded-top-start fs-7"
            (click)="$event.stopPropagation()">
            {{ getCurrentImageIndex(post) + 1 }}/{{ getAllImagesFromGallery(post).length }}
          </div>
        </div>

        <!-- Card content -->
        <div class="card-body p-4">
          <h5 class="fs-5 fw-bold text-dark mb-3 text-truncate cursor-pointer text-hover-dark-blue">
            {{ (post.type).slice(0, 15) }}
          </h5>

          <!-- Property details -->
          <div class="d-flex flex-wrap gap-3 mb-4">
            <div class="border border-gray-200 rounded p-3 flex-grow-1">
              <div class="fs-6 fw-semibold text-dark">{{ getLocalizedAreaName(post.area).slice(0, 15) }}</div>
              <div class="fs-7 text-muted">{{ 'BROKER.MY_ADVERTISEMENTS.AREA' | translate }}</div>
            </div>
            <div class="border border-gray-200 rounded p-3 flex-grow-1">
              <div class="fs-6 fw-semibold text-dark">{{ getLocalizedCityName(post.city).slice(0, 15) }}</div>
              <div class="fs-7 text-muted">{{ 'BROKER.MY_ADVERTISEMENTS.CITY' | translate }}</div>
            </div>
          </div>

          <!-- View button -->
          <a class="btn btn-sm btn-light-dark-blue fw-semibold w-100" (click)="viewPropertyDetails(post)" role="button"
            aria-label="View property details">
            <i class="fa fa-eye me-2"></i>{{ 'BROKER.MY_ADVERTISEMENTS.VIEW' | translate }}
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination - Only show when there are advertisements -->
  <div *ngIf="rows.length > 0" class="mt-5">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<!-- Media Modal -->
<ng-template #mediaModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      {{ selectedMediaType === "video" ? ('BROKER.MY_ADVERTISEMENTS.VIDEO' | translate) :
      ('BROKER.MY_ADVERTISEMENTS.IMAGE' | translate) }}
      <span *ngIf="hasMultipleModalMedia()" class="text-muted ms-2 fs-6">
        ({{ currentModalIndex + 1 }}/{{ modalMediaItems.length }})
      </span>
    </h4>
    <button type="button" class="btn-close bg-danger" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body position-relative">
    <div class="text-center">
      <!-- Video player -->
      <div *ngIf="selectedMediaType === 'video' && selectedMediaUrl" class="video-container">
        <!-- Native video player -->
        <video [src]="selectedMediaUrl" controls autoplay class="w-100" style="max-height: 70vh"
          controlsList="nodownload" preload="auto"></video>

        <!-- Fallback message if video doesn't play -->
        <div class="mt-3 text-muted small">
          <a [href]="selectedMediaUrl" target="_blank">
            <i class="bi bi-box-arrow-up-right me-1"></i>
            {{ 'BROKER.MY_ADVERTISEMENTS.OPEN_IN_NEW_TAB' | translate }}
          </a>
        </div>
      </div>

      <!-- Image viewer -->
      <img *ngIf="selectedMediaType === 'image' && selectedMediaUrl" [src]="selectedMediaUrl" class="img-fluid"
        style="max-height: 70vh" />
    </div>

    <!-- Navigation buttons for multiple media items -->
    <div *ngIf="hasMultipleModalMedia()" class="modal-navigation">
      <!-- Previous button -->
      <button class="btn btn-primary btn-icon position-absolute top-50 start-0 translate-middle-y ms-3"
        (click)="prevModalMedia()" style="z-index: 1050">
        <i class="bi bi-chevron-left"></i>
      </button>

      <!-- Next button -->
      <button class="btn btn-primary btn-icon position-absolute top-50 end-0 translate-middle-y me-3"
        (click)="nextModalMedia()" style="z-index: 1050">
        <i class="bi bi-chevron-right"></i>
      </button>
    </div>
  </div>

  <!-- Modal footer with navigation dots -->
  <div *ngIf="hasMultipleModalMedia()" class="modal-footer justify-content-center">
    <div class="d-flex gap-2">
      <button *ngFor="let item of modalMediaItems; let i = index" class="btn btn-sm rounded-circle p-1"
        [class.btn-primary]="i === currentModalIndex" [class.btn-light]="i !== currentModalIndex"
        (click)="currentModalIndex = i; updateModalMedia()" style="width: 12px; height: 12px"></button>
    </div>
  </div>
</ng-template>

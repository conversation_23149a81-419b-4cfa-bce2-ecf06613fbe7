// File type badge
.file-type-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.9rem;
  text-align: center;
  min-width: 60px;
}

// Background colors
.bg-light {
  &-danger {
    background-color: rgba(241, 65, 108, 0.1);
  }
  &-primary {
    background-color: rgba(0, 163, 255, 0.1);
  }
  &-success {
    background-color: rgba(80, 205, 137, 0.1);
  }
  &-warning {
    background-color: rgba(255, 199, 0, 0.1);
  }
  &-info {
    background-color: rgba(7, 156, 236, 0.1);
  }
  &-secondary {
    background-color: rgba(181, 181, 195, 0.1);
  }
}

// Helper classes
.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.w-100px {
  width: 100px;
}

// Card styles
.card {
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
  }
}

.card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Image styles
.img-fluid {
  object-fit: cover;
  width: 100%;
  height: 120px;
  border-radius: 4px;
}

.cursor-pointer {
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }
}

// Gallery modal
::ng-deep .image-gallery-modal {
  .modal-dialog {
    max-width: 60%;
    margin: 1.75rem auto;
  }

  .modal-content {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    min-height: 80vh;
  }

  .modal-body {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
  }
}

.gallery-image {
  max-width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
  margin: 0 auto;
}

// Navigation buttons
.btn-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

// RTL Support for My Maps
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .card-header {
    .d-flex {
      flex-direction: row-reverse !important;

      .card-title {
        text-align: right;
        order: 2;
      }

      .btn {
        order: 1;

        .bi {
          margin-left: 0.25rem;
          margin-right: 0;
        }
      }
    }
  }

  .modal-header {
    .modal-title {
      text-align: right;
    }

    .btn-close {
      margin-left: 0;
      margin-right: auto;
    }
  }

  .modal-body {
    text-align: right;

    .form-label {
      text-align: right;
    }

    .position-absolute {
      &.start-0 {
        left: auto;
        right: 0;
      }

      &.end-0 {
        right: auto;
        left: 0;
      }
    }
  }

  .modal-footer {
    flex-direction: row-reverse;

    .btn {
      &:first-child {
        margin-left: 0.5rem;
        margin-right: 0;
      }

      // Ensure button content is LTR
      direction: ltr;
    }
  }

  // Form elements alignment
  .form-control {
    text-align: right;
    direction: rtl;
  }

  .form-label {
    text-align: right;
    direction: rtl;
  }

  .text-center {
    &.py-5 {
      text-align: center !important;
    }
  }

  .card-body {
    text-align: right;

    .fw-bold {
      text-align: right;
    }
  }

  // Icon spacing adjustments for RTL
  .me-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }

  // Text alignment
  h3, h4, h5 {
    text-align: right !important;
  }

  p {
    text-align: right !important;
  }

  // Empty state text alignment
  .text-center {
    &.py-5 {
      h5 {
        text-align: center !important;
      }

      p {
        text-align: center !important;
      }
    }
  }

  // Grid layout adjustments
  .row {
    direction: rtl;

    .col-md-3, .col-sm-6 {
      direction: ltr;
    }
  }

  // Icon transformations for RTL
  .bi {
    &.bi-upload {
      transform: scaleX(-1);
    }

    &.bi-chevron-left {
      transform: scaleX(-1);
    }

    &.bi-chevron-right {
      transform: scaleX(-1);
    }
  }

  // Button content direction
  .btn {
    direction: ltr;

    &:hover {
      .bi {
        &.bi-upload {
          transform: scaleX(-1) scale(1.1);
        }
      }
    }
  }

  // Card content alignment
  .card {
    .card-body {
      .fw-bold {
        direction: rtl;
        text-align: right;
      }
    }
  }

  // Override Bootstrap margin classes for RTL
  .me-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
  }

  .ms-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
  }

  // Ensure proper spacing in header
  .card-header {
    .d-flex.justify-content-between.align-items-center {
      > * {
        &:first-child {
          margin-left: auto;
          margin-right: 0;
        }

        &:last-child {
          margin-right: auto;
          margin-left: 0;
        }
      }
    }
  }

  // Global RTL fixes
  * {
    direction: inherit;
  }

  // Specific direction overrides
  .card-title {
    direction: rtl !important;
    text-align: right !important;
  }

  .btn {
    direction: ltr !important;

    // Ensure button text flows correctly
    span, i {
      display: inline-block;
    }
  }
}

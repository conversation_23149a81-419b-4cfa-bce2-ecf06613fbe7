import { BrokerService } from './../../services/broker.service';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { AccountTypeMapper } from '../../account-type-mapper';
import { AuthenticationService } from 'src/app/pages/authentication';
import { PermissionService } from 'src/app/pages/shared/services/permission.service';
import { TranslateService } from '@ngx-translate/core';
import { TranslationService } from '../../../../modules/i18n';

@Component({
  selector: 'app-broker-title',
  templateUrl: './broker-title.component.html',
  styleUrl: './broker-title.component.scss',
})

export class BrokerTitleComponent implements OnInit {
  @Input() showCreateButton: boolean = true;
  user: any = {};
  currentLanguage: string = 'en';

  constructor(
    protected cd: ChangeDetectorRef,
    private brokerService: BrokerService,
    private authenticationService:AuthenticationService,
    public permissionService:PermissionService,
    private translateService: TranslateService,
    private translationService: TranslationService
  ) {
    // Initialize language
    this.currentLanguage = this.translationService.getCurrentLanguage();
    this.translateService.use(this.currentLanguage);
  }

  ngOnInit(): void {
    // let currentUser = localStorage.getItem('currentUser');
    // let userId = currentUser ? JSON.parse(currentUser).id : null;
    // this.getUser(userId);
    this.user = this.authenticationService.getSessionUser();
  }

  getUser(id: any) {
    this.brokerService.getById(id).subscribe({
      next: (response: any) => {
        console.log(response);
        this.user = response?.data ?? {};
        this.cd.detectChanges();
      },
      error: (error: any) => {
        Swal.fire(error.error.message, '', 'error');
      },
    });
  }

  capitalizeWords(text: string | null): string {
    if (!text) return '';
    return text.replace(/\b\w/g, (char) => char.toUpperCase());
  }

  getAccountTypeBadge(type: string): string {
    return AccountTypeMapper.getAccountTypeBadge(type);
  }

  hasPermission(permission: string){
    console.log(this.permissionService.hasPermission(permission));
    return this.permissionService.hasPermission(permission);
  }

  // Get localized area name
  getLocalizedAreaName(area: any): string {
    if (!area) return '';

    if (this.currentLanguage === 'ar') {
      return area.name_ar || area.ar || area.name_en || area.en || '';
    } else {
      return area.name_en || area.en || area.name_ar || area.ar || '';
    }
  }

  // Get localized user name
  getLocalizedUserName(user: any): string {
    if (!user) return '';

    // إذا كانت اللغة عربية، نحاول نجيب الاسم العربي أو نستخدم الاسم الإنجليزي
    if (this.currentLanguage === 'ar') {
      return user.fullName_ar || user.arabicName || user.fullName || '';
    } else {
      return user.fullName || user.englishName || '';
    }
  }

  // Format name properly for display
  formatUserName(user: any): string {
    const name = this.getLocalizedUserName(user);
    return this.capitalizeWords(name);
  }

  // Get translated account type
  getTranslatedAccountType(accountType: string): string {
    if (!accountType) return '';

    const upperType = accountType.toUpperCase();
    const translationKey = `ACCOUNT_TYPES.${upperType}`;

    // Try to get translation, if not found return capitalized original
    const translated = this.translateService.instant(translationKey);
    if (translated !== translationKey) {
      return translated;
    }

    // Fallback to capitalized original
    return this.capitalizeWords(accountType);
  }
}

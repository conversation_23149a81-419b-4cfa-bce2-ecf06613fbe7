.card-shadow {
  box-shadow: 0 10px 30px rgba(253, 0, 0, 0.08); /* bottom-heavy shadow */
  transition: box-shadow 0.3s ease;
  background:
    linear-gradient(60deg, #fff, #cfe2ff); /* peach-pink gradient */
  border-radius: 16px;
}

// RTL Support for Subscription
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .card {
    .card-body {
      text-align: right;

      .d-flex {
        &.flex-center {
          text-align: center;
        }

        &.flex-wrap {
          flex-direction: row-reverse;
        }
      }

      .btn {
        .fa-solid {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  .border {
    text-align: center;
  }

  .fw-semibold {
    text-align: center;
  }

  .fs-1, .fs-3 {
    text-align: center;
  }

  .fw-semibold.text-gray-500.mb-6 {
    text-align: center;
  }

  // إصلاح العناصر لتكون في صف واحد
  .d-flex.flex-center.flex-wrap.mb-5 {
    flex-wrap: nowrap !important; // منع الكسر لأسفل
    justify-content: space-between !important; // توزيع متساوي
    gap: 0.5rem !important; // مسافة صغيرة بين العناصر

    .border.border-dashed.rounded {
      flex: 1 !important; // كل عنصر يأخذ مساحة متساوية
      min-width: auto !important; // إزالة الحد الأدنى للعرض
      max-width: none !important;
      margin: 0 0.25rem !important; // margin صغير
      padding: 0.75rem 0.5rem !important; // padding أصغر
      text-align: center !important;

      .fs-4 {
        font-size: 1.1rem !important; // تصغير الرقم قليلاً
        margin-bottom: 0.25rem !important;
      }

      .fw-semibold {
        font-size: 0.8rem !important; // تصغير النص
        line-height: 1.2 !important;
      }
    }
  }

  // إصلاح للشاشات الصغيرة
  @media (max-width: 768px) {
    .d-flex.flex-center.flex-wrap.mb-5 {
      flex-wrap: wrap !important; // السماح بالكسر في الشاشات الصغيرة

      .border.border-dashed.rounded {
        flex: 1 1 45% !important; // عنصرين في كل صف
        margin-bottom: 0.5rem !important;
      }
    }
  }

  // إصلاح للشاشات الكبيرة جداً
  @media (min-width: 1200px) {
    .d-flex.flex-center.flex-wrap.mb-5 {
      .border.border-dashed.rounded {
        padding: 1rem 0.75rem !important; // padding أكبر

        .fs-4 {
          font-size: 1.25rem !important;
        }

        .fw-semibold {
          font-size: 0.875rem !important;
        }
      }
    }
  }
}

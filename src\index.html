<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>Easy Deal</title>
  <base href="/" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="shortcut icon" href="./assets/media/easydeallogos/home-logo.png" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700">
  <!-- Arabic Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap"
    rel="stylesheet">
  <!-- SPLASH SCREEN-->
  <link rel="stylesheet" id="layout-styles-anchor" href="./assets/splash-screen.css" />

  <link rel="stylesheet" href="https://unpkg.com/@cometchat-pro/chat@latest/CometChat.css" />
  <script type="text/javascript" src="https://unpkg.com/@cometchat-pro/chat@latest/CometChat.js"></script>
  <script type="text/javascript" src="https://unpkg.com/@cometchat/uikit@latest/CometChatUi.js"></script>
  <script src="https://unpkg.com/@cometchat/uikit@3.0.0-beta1/CometChatUiKit.js"></script>
  <script type="text/javascript" src="https://widget-js.cometchat.io/v3/cometchatwidget.js"></script>

</head>

<body root id="kt_body" class="mat-typography">
  <!-- <body root id="kt_body" class="mat-typography" direction="rtl" dir="rtl" style="direction: rtl"></body> -->
  <!--begin::Theme mode setup on page load-->
  <script>
    if (document.documentElement) {
      var defaultThemeMode = "system";

      var hasKTName = document.body.hasAttribute("data-kt-name");
      var lsKey = "kt_" + (hasKTName ? name + "_" : "") + "theme_mode_value"
      var themeMode = localStorage.getItem(lsKey);
      if (!themeMode) {
        if (defaultThemeMode === "system") {
          themeMode =
            window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
        } else {
          themeMode = defaultThemeMode;
        }
      }

      document.documentElement.setAttribute("data-bs-theme", themeMode);
    }
  </script>
  <!--end::Theme mode setup on page load-->
  <div id="splash-screen" class="splash-screen">
    <img src="./assets//media//easydeallogos/loading-logo.png" alt="Easy Deal Logo" class="w-30" />
    <span>Loading ...</span>
  </div>
</body>

</html>

<div class="broker-pages">
  <app-broker-title></app-broker-title>

  <div class="row mt-7">
    <div class="col-md-4 mb-2" *ngFor="let row of rows">
      <div class="card card-shadow h-100" *ngIf="row.name !== 'Free'">
        <div class="card-body d-flex flex-center flex-column py-9 px-13">
          <div class="symbol symbol-65px symbol-circle mb-5">
            <img class="mx-auto h-65px" [src]="row.image" alt="Golden Account" *ngIf="row.name === 'Golden Account'" />
            <img class="mx-auto h-65px" [src]="row.image" alt="Silver Account" *ngIf="row.name === 'Silver Account'" />
            <img class="mx-auto h-65px" [src]="row.image" alt="Bronze Account" *ngIf="row.name === 'Bronze Account'" />
          </div>

          <span class="fs-1 text-dark-blue fw-bold mb-2">{{ getTranslatedAccountName(row.name) }}</span>
          <span class="fs-3 text-dark-blue fw-bold mb-5">{{ row.price }} {{ 'BROKER.SUBSCRIPTION.EGP' | translate }}
            <sub>{{ 'BROKER.SUBSCRIPTION.ANNUAL' | translate }}</sub></span>

          <div class="fw-semibold text-gray-500 mb-6 fs-4 px-13 text-center">
            {{ row.description }}
          </div>

          <div class="d-flex flex-center flex-wrap mb-5">
            <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
              <div class="fs-4 fw-bold text-gray-700">{{ row.maxSpecializations }}</div>
              <div class="fw-semibold text-gray-500">{{ 'BROKER.SUBSCRIPTION.SPECIALIZATIONS' | translate }}</div>
            </div>

            <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
              <div class="fs-4 fw-bold text-gray-700">{{ row.maxLocations }}</div>
              <div class="fw-semibold text-gray-500">{{ 'BROKER.SUBSCRIPTION.LOCATIONS' | translate }}</div>
            </div>

            <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
              <div class="fs-4 fw-bold text-gray-700">{{ row.maxAdvertisements }}</div>
              <div class="fw-semibold text-gray-500">{{ 'BROKER.SUBSCRIPTION.ADVERTISEMENTS' | translate }}</div>
            </div>

            <div class="border border-dashed rounded min-w-50px py-3 px-4 mx-2 mb-3">
              <div class="fs-4 fw-bold text-gray-700">{{ row.maxOperations }}</div>
              <div class="fw-semibold text-gray-500">{{ 'BROKER.SUBSCRIPTION.OPERATIONS' | translate }}</div>
            </div>
          </div>

          <button class="btn btn-md fw-bolder btn-dark-blue btn-flex btn-center" data-kt-follow-btn="true">
            <i class="fa-solid fa-basket-shopping"></i>
            {{ 'BROKER.SUBSCRIPTION.SUBSCRIBE' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

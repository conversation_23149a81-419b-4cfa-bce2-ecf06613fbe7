.image-navigation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.position-relative:hover .image-navigation {
  opacity: 1;
}

.image-nav-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  i {
    font-size: 12px;
  }
}

.prev-btn {
  margin-right: auto;
}

.next-btn {
  margin-left: auto;
}

.image-counter {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  z-index: 10;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0.25rem;

  i {
    font-size: 40px;
    color: white;
    opacity: 0.9;
  }
}

.gallery-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(253, 0, 0, 0.08);
  border-radius: 5%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  i {
    font-size: 14px;
    color: white;
  }
}

.media-container:hover .gallery-overlay {
  opacity: 1;
}

.media-container {
  position: relative;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }

  img {
    transition: filter 0.2s;
  }

  &:hover img {
    filter: brightness(1.1);
  }

  // Add a subtle overlay to indicate it's clickable
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent 0%,
      rgba(0, 123, 255, 0.1) 50%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 0.25rem;
  }

  &:hover::after {
    opacity: 1;
  }
}

// Card hover effects
.card.cursor-pointer {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

// Page Header Styles
.page-header {
  border: 1px solid #E4E6EF;
  margin: 0 1.5rem;

  .symbol-label {
    border-radius: 12px;
  }

  h1 {
    font-size: 1.75rem;
    margin-bottom: 0.25rem;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .btn {
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// Responsive header
@media (max-width: 768px) {
  .page-header {
    margin: 0 0.5rem;
    padding: 1.5rem !important;

    .d-flex.align-items-center.justify-content-between {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .d-flex.align-items-center.gap-3 {
      width: 100%;
      justify-content: flex-end;
    }

    h1 {
      font-size: 1.5rem;
    }
  }
}

// Empty State Styles
.empty-state-container {
  min-height: 400px;
  padding: 3rem 1rem;
}

.empty-state-content {
  max-width: 500px;
  margin: 0 auto;
}

.empty-state-icon {
  .symbol-label {
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
}

.empty-state-text {
  h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
  }

  p {
    line-height: 1.6;
    color: #6c757d;
  }
}

.empty-state-action {
  .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }
  }
}

.empty-state-decoration {
  .decoration-item {
    animation: float 3s ease-in-out infinite;

    &:nth-child(2) {
      animation-delay: -1s;
    }

    &:nth-child(3) {
      animation-delay: -2s;
    }

    .symbol-label {
      border-radius: 50%;
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .empty-state-container {
    min-height: 300px;
    padding: 2rem 1rem;
  }

  .empty-state-text h3 {
    font-size: 1.5rem;
  }

  .empty-state-decoration {
    .d-flex {
      gap: 1rem !important;
    }
  }
}

// Styles for the media modal
::ng-deep .media-modal {
  .modal-dialog {
    max-width: 800px;
  }

  .modal-content {
    background-color: #fff;
  }

  .modal-header {
    border-bottom-color: #333;
  }

  .btn-close {
    filter: invert(1);
  }

  video {
    width: 100%;
    max-height: 70vh;
    outline: none;
  }
}

// RTL Support for My Advertisements
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .page-header {
    .d-flex {
      &.justify-content-between {
        flex-direction: row-reverse;
      }

      &.align-items-center {
        flex-direction: row-reverse !important;

        .symbol {
          margin-left: 0;
          margin-right: 1rem;
          order: 2; // الأيقونة في أقصى الشمال
        }

        div {
          text-align: right;
          order: 1; // النص في اليمين

          h1 {
            text-align: right;
          }

          p {
            text-align: right;
          }
        }
      }

      &.gap-3 {
        .btn {
          .fas {
            margin-left: 0.5rem;
            margin-right: 0;
          }
        }
      }
    }
  }

  .card {
    .card-body {
      text-align: right;

      .d-flex {
        &.flex-wrap {
          flex-direction: row-reverse;
        }

        .border {
          text-align: right;
        }
      }

      .btn {
        .fa {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  .modal-header {
    .modal-title {
      text-align: right;
    }

    .btn-close {
      margin-left: 0;
      margin-right: auto;
    }
  }

  .modal-body {
    text-align: right;

    .position-absolute {
      &.start-0 {
        left: auto;
        right: 0;
      }

      &.end-0 {
        right: auto;
        left: 0;
      }
    }
  }

  .empty-state-container {
    text-align: right;

    .empty-state-text {
      text-align: right;
    }
  }

  .image-navigation {
    .btn {
      &:first-child {
        order: 2;
      }

      &:last-child {
        order: 1;
      }
    }
  }

  .image-counter {
    right: auto;
    left: 10px;
  }

  .gallery-overlay {
    right: auto;
    left: 8px;
  }

  .prev-btn {
    margin-right: 0;
    margin-left: auto;
  }

  .next-btn {
    margin-left: 0;
    margin-right: auto;
  }

  // Additional RTL fixes for page header
  .page-header {
    .d-flex.align-items-center.justify-content-between {
      flex-direction: row-reverse !important;

      > div:first-child {
        order: 2;
        justify-self: flex-end;

        .d-flex.align-items-center {
          flex-direction: row-reverse !important;

          .symbol {
            margin-left: 0;
            margin-right: 1rem;
            order: 2; // الأيقونة في أقصى الشمال
          }

          > div {
            order: 1; // النص في اليمين
          }
        }
      }

      > div:last-child {
        order: 1; // زر الإضافة في أقصى الشمال
        justify-self: flex-start;
      }
    }
  }

  // تأكيد ترتيب العناصر في الـ header
  .page-header .d-flex.align-items-center.justify-content-between {
    > div:first-child .d-flex.align-items-center {
      width: 100%;
      justify-content: flex-end; // محاذاة المحتوى لليمين

      .symbol {
        flex-shrink: 0; // منع تقليص الأيقونة
      }

      > div {
        flex-grow: 1; // السماح للنص بأخذ المساحة المتبقية
        margin-right: auto; // دفع النص لليمين
      }
    }
  }

  // Specific targeting for the symbol positioning
  .symbol.symbol-50px {
    &.me-4 {
      margin-right: 0 !important;
      margin-left: 1.5rem !important;
    }
  }

  // Badge positioning
  .badge {
    margin-right: 0.5rem;
    margin-left: 0;
  }

  // Text alignment for Arabic
  h1, h3, h4, h5 {
    text-align: right !important;
  }

  p {
    text-align: right !important;
  }

  // Icon spacing adjustments for RTL
  .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  .me-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }

  .ms-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
  }

  // Button icon positioning
  .btn {
    .fas, .fa {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;
    }
  }

  // Icon transformations for RTL
  .symbol-label {
    .fas {
      &.fa-bullhorn {
        transform: scaleX(-1) !important;
      }

      &.fa-plus {
        // Plus icon doesn't need transformation
      }
    }
  }

  // Specific targeting for the header icon
  .page-header {
    .symbol-label.bg-light-primary {
      .fas.fa-bullhorn {
        transform: scaleX(-1) !important;
      }
    }
  }

  // Additional icon fixes
  .fas, .fa {
    &.fa-bullhorn {
      transform: scaleX(-1);
    }

    &.fa-eye {
      transform: scaleX(-1);
    }

    &.fa-chevron-left {
      transform: scaleX(-1);
    }

    &.fa-chevron-right {
      transform: scaleX(-1);
    }
  }

  .bi {
    &.bi-chevron-left {
      transform: scaleX(-1);
    }

    &.bi-chevron-right {
      transform: scaleX(-1);
    }

    &.bi-play-circle-fill {
      transform: scaleX(-1);
    }
  }

  // Global icon transformations for directional icons
  i {
    &.fas.fa-bullhorn,
    &.fa.fa-bullhorn {
      transform: scaleX(-1) !important;
    }

    &.fas.fa-eye,
    &.fa.fa-eye {
      transform: scaleX(-1) !important;
    }
  }

  // Force the icon container to be on the right
  .page-header {
    .d-flex.align-items-center:first-child {
      width: 100%;
      justify-content: flex-end !important;

      .symbol {
        position: relative;
        right: 0;
        left: auto;
      }

      > div:last-child {
        flex: 1;
        margin-right: 1rem;
        margin-left: 0;
      }
    }
  }

  // Override Bootstrap flex utilities for RTL
  .d-flex {
    &.align-items-center {
      &:first-child {
        flex-direction: row-reverse !important;
      }
    }
  }

  // تحديد ترتيب العناصر بشكل نهائي
  .page-header {
    .d-flex.align-items-center:first-child {
      .symbol.symbol-50px.me-4 {
        order: 2 !important; // الأيقونة في أقصى الشمال
        margin-left: 0 !important;
        margin-right: 1rem !important;
      }

      > div:last-child {
        order: 1 !important; // النص في اليمين
        text-align: right !important;
        flex-grow: 1;
      }
    }
  }

  // إصلاح margin classes
  .me-4 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }

  // إصلاح حجم وشكل الأزرار في اللغة العربية
  .btn {
    min-width: 120px !important; // حد أدنى لعرض الزر
    padding: 0.5rem 1rem !important; // زيادة الـ padding
    white-space: nowrap !important; // منع كسر النص
    font-size: 0.9rem !important; // حجم خط مناسب

    &.btn-sm {
      min-width: 100px !important;
      padding: 0.375rem 0.75rem !important;
      font-size: 0.85rem !important; // حجم خط أكبر للأزرار الصغيرة
    }

    &.btn-lg {
      font-size: 1.1rem !important;
    }

    // تحسين شكل الزر
    border-radius: 6px !important;
    font-weight: 600 !important;

    // إصلاح ترتيب الأيقونة والنص
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-direction: row-reverse !important; // الأيقونة بعد النص في العربية

    i {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
      font-size: inherit !important; // الأيقونة تأخذ نفس حجم النص
    }
  }

  // إصلاح أزرار الـ header خصوصاً
  .page-header {
    .btn {
      min-width: 130px !important;
      height: 40px !important;
      font-size: 0.95rem !important; // حجم خط أكبر للـ header
      padding: 0.5rem 1.2rem !important;

      &.btn-sm {
        font-size: 0.9rem !important; // حجم خط أكبر حتى للأزرار الصغيرة في الـ header
        height: 38px !important;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  // إصلاح النص في الأزرار
  .btn {
    span, .btn-text {
      display: inline-block;
      line-height: 1.2;
    }

    // ضمان عدم اختفاء النص
    overflow: visible !important;
    text-overflow: unset !important;

    // تحسين المظهر العام
    transition: all 0.3s ease;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(13, 71, 161, 0.25);
    }
  }

  // إصلاح خاص للأزرار الصغيرة
  .btn-sm {
    .fas, .fa {
      font-size: 0.85rem !important; // حجم أيقونة أكبر
    }
  }

  // إصلاح عام لحجم الأيقونات
  .btn {
    .fas, .fa, i {
      font-size: 0.9rem !important; // حجم أيقونة مناسب
    }

    &.btn-lg {
      .fas, .fa, i {
        font-size: 1rem !important;
      }
    }
  }

  // إصلاح margin للأيقونات في الأزرار
  .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  // ضمان وضوح النص في الأزرار
  .btn {
    line-height: 1.4 !important;
    letter-spacing: 0.02em !important; // تباعد أحرف أفضل

    // إزالة أي تأثيرات قد تصغر النص
    transform: none !important;

    // ضمان عدم تأثر حجم النص بأي أنماط أخرى
    * {
      font-size: inherit !important;
    }
  }

  // إصلاح خاص لزر الإضافة في الـ header
  .page-header .btn-primary {
    font-size: 0.95rem !important;
    font-weight: 600 !important;

    .fas.fa-plus {
      font-size: 0.9rem !important;
      font-weight: 900 !important;
    }
  }

  // إصلاح Empty State في اللغة العربية - كل شيء في الوسط
  .empty-state-container {
    text-align: center !important;

    .empty-state-content {
      text-align: center !important;

      .empty-state-icon {
        text-align: center !important; // الأيقونة في الوسط
      }

      .empty-state-text {
        text-align: center !important;

        h3 {
          text-align: center !important;
        }

        p {
          text-align: center !important;
          margin-left: auto !important;
          margin-right: auto !important;
        }
      }

      .empty-state-action {
        text-align: center !important;

        .btn {
          margin-left: auto !important;
          margin-right: auto !important;
        }
      }

      .empty-state-decoration {
        text-align: center !important; // العناصر الزخرفية في الوسط
      }
    }
  }

  // إصلاح البطاقات في اللغة العربية
  .row.g-4 {
    .col-md-3 {
      .card {
        .card-body {
          text-align: right !important;

          h5 {
            text-align: right !important;
          }

          .d-flex.flex-wrap {
            .border {
              text-align: right !important;

              .fs-6, .fs-7 {
                text-align: right !important;
              }
            }
          }

          .btn {
            text-align: center !important;

            .fa {
              margin-left: 0.5rem !important;
              margin-right: 0 !important;
            }
          }
        }
      }
    }
  }

  // إصلاح إضافي للنصوص والأزرار
  .tab-pane {
    .empty-state-container {
      .text-center {
        text-align: center !important;

        &.py-8 {
          text-align: center !important;
        }
      }
    }

    .row.g-4 {
      direction: rtl;

      .col-md-3, .col-sm-6, .col-12 {
        direction: ltr;

        .card {
          direction: rtl;
        }
      }
    }
  }

  // إصلاح Pagination
  .mt-5 {
    text-align: center !important;
  }

  // تأكيد أن Empty State في الوسط
  .empty-state-container.d-flex.flex-column.align-items-center.justify-content-center {
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;

    .empty-state-content.text-center {
      text-align: center !important;
      width: 100%;

      .empty-state-text.mb-6 {
        text-align: center !important;

        h3.text-dark.fw-bold.mb-3 {
          text-align: center !important;
        }

        p.text-muted.fs-6.mb-0.mx-auto {
          text-align: center !important;
        }
      }

      .empty-state-action {
        text-align: center !important;

        .btn.btn-primary.btn-lg {
          display: inline-block !important;
          margin: 0 auto !important;
        }
      }
    }
  }
}

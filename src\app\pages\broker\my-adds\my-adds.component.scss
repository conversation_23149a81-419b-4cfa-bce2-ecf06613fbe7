.image-navigation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.position-relative:hover .image-navigation {
  opacity: 1;
}

.image-nav-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  i {
    font-size: 12px;
  }
}

.prev-btn {
  margin-right: auto;
}

.next-btn {
  margin-left: auto;
}

.image-counter {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  z-index: 10;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0.25rem;

  i {
    font-size: 40px;
    color: white;
    opacity: 0.9;
  }
}

.gallery-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(253, 0, 0, 0.08);
  border-radius: 5%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  i {
    font-size: 14px;
    color: white;
  }
}

.media-container:hover .gallery-overlay {
  opacity: 1;
}

.media-container {
  position: relative;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }

  img {
    transition: filter 0.2s;
  }

  &:hover img {
    filter: brightness(1.1);
  }

  // Add a subtle overlay to indicate it's clickable
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent 0%,
      rgba(0, 123, 255, 0.1) 50%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 0.25rem;
  }

  &:hover::after {
    opacity: 1;
  }
}

// Card hover effects
.card.cursor-pointer {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

// Page Header Styles
.page-header {
  border: 1px solid #E4E6EF;
  margin: 0 1.5rem;

  .symbol-label {
    border-radius: 12px;
  }

  h1 {
    font-size: 1.75rem;
    margin-bottom: 0.25rem;
  }

  .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .btn {
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// Responsive header
@media (max-width: 768px) {
  .page-header {
    margin: 0 0.5rem;
    padding: 1.5rem !important;

    .d-flex.align-items-center.justify-content-between {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .d-flex.align-items-center.gap-3 {
      width: 100%;
      justify-content: flex-end;
    }

    h1 {
      font-size: 1.5rem;
    }
  }
}

// Empty State Styles
.empty-state-container {
  min-height: 400px;
  padding: 3rem 1rem;
}

.empty-state-content {
  max-width: 500px;
  margin: 0 auto;
}

.empty-state-icon {
  .symbol-label {
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
}

.empty-state-text {
  h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
  }

  p {
    line-height: 1.6;
    color: #6c757d;
  }
}

.empty-state-action {
  .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }
  }
}

.empty-state-decoration {
  .decoration-item {
    animation: float 3s ease-in-out infinite;

    &:nth-child(2) {
      animation-delay: -1s;
    }

    &:nth-child(3) {
      animation-delay: -2s;
    }

    .symbol-label {
      border-radius: 50%;
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .empty-state-container {
    min-height: 300px;
    padding: 2rem 1rem;
  }

  .empty-state-text h3 {
    font-size: 1.5rem;
  }

  .empty-state-decoration {
    .d-flex {
      gap: 1rem !important;
    }
  }
}

// Styles for the media modal
::ng-deep .media-modal {
  .modal-dialog {
    max-width: 800px;
  }

  .modal-content {
    background-color: #fff;
  }

  .modal-header {
    border-bottom-color: #333;
  }

  .btn-close {
    filter: invert(1);
  }

  video {
    width: 100%;
    max-height: 70vh;
    outline: none;
  }
}

// RTL Support for My Advertisements
:host-context(html[dir="rtl"]),
:host-context(html[lang="ar"]),
.rtl {
  .page-header {
    .d-flex {
      &.justify-content-between {
        flex-direction: row-reverse;
      }

      &.align-items-center {
        flex-direction: row-reverse !important;

        .symbol {
          margin-left: 0;
          margin-right: 1rem;
          order: 2; // الأيقونة في أقصى الشمال
        }

        div {
          text-align: right;
          order: 1; // النص في اليمين

          h1 {
            text-align: right;
          }

          p {
            text-align: right;
          }
        }
      }

      &.gap-3 {
        .btn {
          .fas {
            margin-left: 0.5rem;
            margin-right: 0;
          }
        }
      }
    }
  }

  .card {
    .card-body {
      text-align: right;

      .d-flex {
        &.flex-wrap {
          flex-direction: row-reverse;
        }

        .border {
          text-align: right;
        }
      }

      .btn {
        .fa {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  .modal-header {
    .modal-title {
      text-align: right;
    }

    .btn-close {
      margin-left: 0;
      margin-right: auto;
    }
  }

  .modal-body {
    text-align: right;

    .position-absolute {
      &.start-0 {
        left: auto;
        right: 0;
      }

      &.end-0 {
        right: auto;
        left: 0;
      }
    }
  }

  .empty-state-container {
    text-align: right;

    .empty-state-text {
      text-align: right;
    }
  }

  .image-navigation {
    .btn {
      &:first-child {
        order: 2;
      }

      &:last-child {
        order: 1;
      }
    }
  }

  .image-counter {
    right: auto;
    left: 10px;
  }

  .gallery-overlay {
    right: auto;
    left: 8px;
  }

  .prev-btn {
    margin-right: 0;
    margin-left: auto;
  }

  .next-btn {
    margin-left: 0;
    margin-right: auto;
  }

  // Additional RTL fixes for page header
  .page-header {
    .d-flex.align-items-center.justify-content-between {
      flex-direction: row-reverse !important;

      > div:first-child {
        order: 2;
        justify-self: flex-end;

        .d-flex.align-items-center {
          flex-direction: row-reverse !important;

          .symbol {
            margin-left: 0;
            margin-right: 1rem;
            order: 2; // الأيقونة في أقصى الشمال
          }

          > div {
            order: 1; // النص في اليمين
          }
        }
      }

      > div:last-child {
        order: 1; // زر الإضافة في أقصى الشمال
        justify-self: flex-start;
      }
    }
  }

  // تأكيد ترتيب العناصر في الـ header
  .page-header .d-flex.align-items-center.justify-content-between {
    > div:first-child .d-flex.align-items-center {
      width: 100%;
      justify-content: flex-end; // محاذاة المحتوى لليمين

      .symbol {
        flex-shrink: 0; // منع تقليص الأيقونة
      }

      > div {
        flex-grow: 1; // السماح للنص بأخذ المساحة المتبقية
        margin-right: auto; // دفع النص لليمين
      }
    }
  }

  // Specific targeting for the symbol positioning
  .symbol.symbol-50px {
    &.me-4 {
      margin-right: 0 !important;
      margin-left: 1.5rem !important;
    }
  }

  // Badge positioning
  .badge {
    margin-right: 0.5rem;
    margin-left: 0;
  }

  // Text alignment for Arabic
  h1, h3, h4, h5 {
    text-align: right !important;
  }

  p {
    text-align: right !important;
  }

  // Icon spacing adjustments for RTL
  .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  .me-4 {
    margin-right: 0 !important;
    margin-left: 1.5rem !important;
  }

  .ms-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
  }

  // Button icon positioning
  .btn {
    .fas, .fa {
      margin-right: 0 !important;
      margin-left: 0.5rem !important;
    }
  }

  // Icon transformations for RTL
  .symbol-label {
    .fas {
      &.fa-bullhorn {
        transform: scaleX(-1) !important;
      }

      &.fa-plus {
        // Plus icon doesn't need transformation
      }
    }
  }

  // Specific targeting for the header icon
  .page-header {
    .symbol-label.bg-light-primary {
      .fas.fa-bullhorn {
        transform: scaleX(-1) !important;
      }
    }
  }

  // Additional icon fixes
  .fas, .fa {
    &.fa-bullhorn {
      transform: scaleX(-1);
    }

    &.fa-eye {
      transform: scaleX(-1);
    }

    &.fa-chevron-left {
      transform: scaleX(-1);
    }

    &.fa-chevron-right {
      transform: scaleX(-1);
    }
  }

  .bi {
    &.bi-chevron-left {
      transform: scaleX(-1);
    }

    &.bi-chevron-right {
      transform: scaleX(-1);
    }

    &.bi-play-circle-fill {
      transform: scaleX(-1);
    }
  }

  // Global icon transformations for directional icons
  i {
    &.fas.fa-bullhorn,
    &.fa.fa-bullhorn {
      transform: scaleX(-1) !important;
    }

    &.fas.fa-eye,
    &.fa.fa-eye {
      transform: scaleX(-1) !important;
    }
  }

  // Force the icon container to be on the right
  .page-header {
    .d-flex.align-items-center:first-child {
      width: 100%;
      justify-content: flex-end !important;

      .symbol {
        position: relative;
        right: 0;
        left: auto;
      }

      > div:last-child {
        flex: 1;
        margin-right: 1rem;
        margin-left: 0;
      }
    }
  }

  // Override Bootstrap flex utilities for RTL
  .d-flex {
    &.align-items-center {
      &:first-child {
        flex-direction: row-reverse !important;
      }
    }
  }

  // تحديد ترتيب العناصر بشكل نهائي
  .page-header {
    .d-flex.align-items-center:first-child {
      .symbol.symbol-50px.me-4 {
        order: 2 !important; // الأيقونة في أقصى الشمال
        margin-left: 0 !important;
        margin-right: 1rem !important;
      }

      > div:last-child {
        order: 1 !important; // النص في اليمين
        text-align: right !important;
        flex-grow: 1;
      }
    }
  }

  // إصلاح margin classes
  .me-4 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
  }
}
